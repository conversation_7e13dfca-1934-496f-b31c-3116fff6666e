package store

import "fmt"

type User struct {
	ID    int
	Name  string
	Email string
}

type UserStore struct {
	Users []User
	IDgenerator
}

type counter func() int

func (c counter) incrementCounter() {
	count := 0

	return func() int {
		count++
		return count
	}
}

func NewUserStore() UserStore {
	NextID := counter()
	return UserStore{
		Users: make([]User, 0),
	}
}

func (u *UserStore) AddUser(name, email string) error {

	if u.AlreadyExists(email) {
		return fmt.Errorf("user already exists")
	}

	id := 0
	id++
	u.Users = append(u.Users, User{id, name, email})
	return nil
}

func (u *UserStore) AlreadyExists(email string) bool {
	for _, user := range u.GetUsers() {
		if user.Email == email {
			return true
		}
	}
	return false
}
func (u *UserStore) GetUsers() []User {
	return u.Users
}

func (u *UserStore) ClearData() {
	u.Users = make([]User, 0)
}

func (u *UserStore) InitData() {
	u.AddUser("bob", "<EMAIL>")
	u.AddUser("jane", "<EMAIL>")
}
